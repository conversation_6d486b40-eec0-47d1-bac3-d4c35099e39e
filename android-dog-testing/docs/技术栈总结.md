# 技术栈总结

## 1. 核心技术栈

### 1.1 开发语言和框架
```
开发语言: Kotlin 1.9.0+
UI框架: Jetpack Compose 1.5.0+
架构模式: MVVM + Clean Architecture
异步处理: Kotlin Coroutines + Flow
依赖注入: Hilt 2.47+
```

### 1.2 Android平台技术
```
最低SDK版本: API 24 (Android 7.0)
目标SDK版本: API 34 (Android 14)
编译SDK版本: API 34
Gradle版本: 8.0+
AGP版本: 8.1.0+
```

### 1.3 网络和通信
```
HTTP客户端: Retrofit 2.9.0 + OkHttp 4.11.0
JSON解析: Moshi 1.15.0
WebSocket: OkHttp WebSocket
蓝牙通信: Android Bluetooth API
WiFi管理: Android WiFi API
```

### 1.4 数据存储
```
本地数据库: Room 2.5.0
键值存储: DataStore 1.0.0
文件存储: Android Storage API
缓存管理: OkHttp Cache
```

### 1.5 UI和用户体验
```
UI组件: Jetpack Compose
导航: Navigation Compose 2.7.0
图表显示: Vico 1.12.0
权限处理: Accompanist Permissions
动画效果: Compose Animation
```

## 2. 项目依赖配置

### 2.1 项目级 build.gradle
```gradle
buildscript {
    ext {
        compose_version = '1.5.4'
        kotlin_version = '1.9.10'
        hilt_version = '2.47'
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.google.dagger:hilt-android-gradle-plugin:$hilt_version"
    }
}
```

### 2.2 应用级 build.gradle
```gradle
android {
    compileSdk 34
    
    defaultConfig {
        applicationId "com.robotics.dogtest"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    kotlinOptions {
        jvmTarget = '17'
    }
    
    buildFeatures {
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion compose_version
    }
}

dependencies {
    // Kotlin和协程
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // Android核心库
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.0'
    
    // Jetpack Compose
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation "androidx.compose.material3:material3:1.1.2"
    implementation "androidx.compose.runtime:runtime-livedata:$compose_version"
    
    // 导航
    implementation "androidx.navigation:navigation-compose:2.7.4"
    
    // ViewModel
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    
    // 依赖注入
    implementation "com.google.dagger:hilt-android:$hilt_version"
    kapt "com.google.dagger:hilt-compiler:$hilt_version"
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0'
    
    // 网络通信
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-moshi:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'
    
    // JSON解析
    implementation 'com.squareup.moshi:moshi-kotlin:1.15.0'
    kapt 'com.squareup.moshi:moshi-kotlin-codegen:1.15.0'
    
    // 数据库
    implementation 'androidx.room:room-runtime:2.5.0'
    implementation 'androidx.room:room-ktx:2.5.0'
    kapt 'androidx.room:room-compiler:2.5.0'
    
    // DataStore
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    
    // 权限处理
    implementation 'com.google.accompanist:accompanist-permissions:0.32.0'
    
    // 图表
    implementation 'com.patrykandpatrick.vico:compose:1.12.0'
    implementation 'com.patrykandpatrick.vico:compose-m3:1.12.0'
    
    // 日志
    implementation 'com.jakewharton.timber:timber:5.0.1'
    
    // 测试
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.5.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
}
```

## 3. 权限配置

### 3.1 AndroidManifest.xml权限
```xml
<!-- 蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

<!-- WiFi权限 -->
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

<!-- 位置权限 (蓝牙扫描需要) -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- 网络权限 -->
<uses-permission android:name="android.permission.INTERNET" />

<!-- 存储权限 -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

<!-- 前台服务权限 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

### 3.2 功能声明
```xml
<!-- 蓝牙功能 -->
<uses-feature
    android:name="android.hardware.bluetooth_le"
    android:required="true" />

<!-- WiFi功能 -->
<uses-feature
    android:name="android.hardware.wifi"
    android:required="true" />
```

## 4. 架构组件配置

### 4.1 Hilt配置
```kotlin
@HiltAndroidApp
class DogTestApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        Timber.plant(Timber.DebugTree())
    }
}

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = HttpLoggingInterceptor.Level.BODY
            })
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build()
    }
    
    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl("http://192.168.4.1:8080/api/v1/")
            .client(okHttpClient)
            .addConverterFactory(MoshiConverterFactory.create())
            .build()
    }
}
```

### 4.2 Room数据库配置
```kotlin
@Database(
    entities = [TestResult::class, Device::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class TestDatabase : RoomDatabase() {
    abstract fun testResultDao(): TestResultDao
    abstract fun deviceDao(): DeviceDao
}

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): TestDatabase {
        return Room.databaseBuilder(
            context,
            TestDatabase::class.java,
            "test_database"
        ).build()
    }
}
```

## 5. 性能优化配置

### 5.1 ProGuard配置
```proguard
# Retrofit
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions

# Moshi
-keep class com.squareup.moshi.** { *; }
-keep @com.squareup.moshi.JsonQualifier interface *

# 数据模型类
-keep class com.robotics.dogtest.data.model.** { *; }

# Hilt
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
```

### 5.2 编译优化
```gradle
android {
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}
```

## 6. 开发工具配置

### 6.1 代码质量工具
```gradle
// Detekt静态代码分析
plugins {
    id 'io.gitlab.arturbosch.detekt' version '1.23.1'
}

detekt {
    config = files("$projectDir/config/detekt/detekt.yml")
    buildUponDefaultConfig = true
}

// Ktlint代码格式化
plugins {
    id 'org.jlleitschuh.gradle.ktlint' version '11.6.1'
}
```

### 6.2 测试配置
```gradle
android {
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
    }
}

dependencies {
    // 单元测试
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito:mockito-core:5.5.0'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.1.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    
    // UI测试
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation "androidx.compose.ui:ui-test-junit4:$compose_version"
}
```

这个技术栈配置确保了项目的现代化、可维护性和高性能。所有依赖都是最新稳定版本，支持Android 14的最新特性。
