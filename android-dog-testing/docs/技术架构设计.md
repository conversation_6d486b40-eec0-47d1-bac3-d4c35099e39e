# Android机器狗产测系统 - 技术架构设计

## 1. 整体系统架构

```
┌─────────────────┐    蓝牙BLE     ┌─────────────────┐
│   Android测试端  │ ◄──────────► │   机器狗设备端   │
│   (主控设备)     │              │   (被测设备)     │
└─────────────────┘              └─────────────────┘
         │                                │
         │          WiFi局域网             │
         └────────────────────────────────┘
                    HTTP/WebSocket
```

### 系统组件
- **Android测试端**：测试控制、UI交互、结果分析
- **机器狗设备端**：接受测试指令、执行测试、返回结果
- **通信链路**：蓝牙发现配对 + WiFi数据传输
- **测试协议**：标准化的测试指令和数据格式

## 2. Android应用分层架构

### 2.1 架构模式：Clean Architecture + MVVM

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Compose UI  │  │ ViewModels  │  │ Navigation  │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                     Domain Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  Use Cases  │  │ Repositories│  │   Models    │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                      Data Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Repository  │  │ Data Sources│  │  Database   │     │
│  │    Impl     │  │ Local/Remote│  │    DAOs     │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Bluetooth   │  │    WiFi     │  │   Testing   │     │
│  │  Manager    │  │   Manager   │  │   Engine    │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### 2.2 核心模块设计

#### Presentation Layer (表现层)
```kotlin
// UI组件
- MainActivity
- DeviceListScreen
- TestExecutionScreen  
- TestResultScreen
- SettingsScreen

// ViewModels
- DeviceViewModel
- TestViewModel
- ResultViewModel
- SettingsViewModel
```

#### Domain Layer (业务逻辑层)
```kotlin
// Use Cases
- ScanDevicesUseCase
- ConnectDeviceUseCase
- ExecuteTestUseCase
- GenerateReportUseCase

// Repository接口
- DeviceRepository
- TestRepository
- ResultRepository

// Domain Models
- Device
- TestCase
- TestResult
- TestReport
```

#### Data Layer (数据层)
```kotlin
// Repository实现
- DeviceRepositoryImpl
- TestRepositoryImpl
- ResultRepositoryImpl

// Data Sources
- BluetoothDataSource
- WiFiDataSource
- LocalDataSource
- RemoteDataSource

// Database
- TestDatabase
- DeviceDao
- TestResultDao
```

#### Infrastructure Layer (基础设施层)
```kotlin
// 蓝牙管理
- BluetoothManager
- BleScanner
- GattClient

// WiFi管理
- WiFiManager
- NetworkClient
- HttpClient

// 测试引擎
- TestEngine
- TestExecutor
- TestValidator
```

## 3. 通信协议架构

### 3.1 蓝牙通信协议 (设备发现和配对)

```
应用层：设备信息交换、WiFi配置传输
GATT层：自定义Service和Characteristic
BLE层：低功耗蓝牙协议
物理层：2.4GHz无线通信
```

#### 自定义GATT服务
```
Service UUID: 0000180F-0000-1000-8000-00805F9B34FB
├── Device Info Characteristic (Read)
│   └── 设备型号、序列号、固件版本
├── WiFi Config Characteristic (Write)
│   └── SSID、密码、IP配置
└── Status Characteristic (Notify)
    └── 连接状态、错误信息
```

### 3.2 WiFi通信协议 (测试执行)

```
应用层：测试指令、数据传输、结果回传
传输层：HTTP/HTTPS、WebSocket、TCP
网络层：IP (192.168.4.x 局域网)
数据链路层：WiFi 802.11 b/g/n
物理层：2.4G/5G WiFi
```

#### RESTful API设计
```
GET  /api/device/info          # 获取设备信息
POST /api/test/start           # 开始测试
GET  /api/test/status          # 获取测试状态
GET  /api/test/result/{id}     # 获取测试结果
POST /api/test/stop            # 停止测试
```

#### WebSocket实时通信
```
/ws/test/monitor               # 测试进度监控
/ws/device/status              # 设备状态监控
/ws/logs/stream                # 实时日志流
```

## 4. 测试框架架构

### 4.1 测试引擎设计

```
┌─────────────────────────────────────────────────────────┐
│                    Test Engine                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │Test Executor│  │Test Scheduler│  │Test Reporter│     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │Test Validator│  │Test Monitor │  │Test Logger  │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### 4.2 核心测试模块详细设计

#### 4.2.1 RGB灯光测试模块
```kotlin
// 测试目标：验证RGB LED灯珠的正常工作
class RGBLightTest : BaseTestCase {
    // 测试内容
    - 颜色显示准确性测试
    - 亮度控制功能测试
    - 灯光同步性测试
    - 动态效果测试

    // 技术指标
    - 颜色准确度：RGB值偏差 < 5%
    - 响应时间：< 100ms
    - 亮度范围：0-100%可调

    // 判定方式
    - 自动化：Android摄像头 + 图像识别
    - 人工辅助：目视确认 + 手动标记
}
```

#### 4.2.2 喇叭测试模块
```kotlin
// 测试目标：验证音频输出功能
class SpeakerTest : BaseTestCase {
    // 测试内容
    - 音频输出功能测试
    - 音量控制范围测试
    - 音质和失真度测试
    - 多频段响应测试

    // 技术指标
    - 音量范围：40-85dB
    - 频率响应：100Hz-8kHz ±3dB
    - 总谐波失真：< 5%

    // 判定方式
    - Android设备麦克风测量分贝值
    - 播放扫频信号分析频谱
}
```

#### 4.2.3 显示屏测试模块
```kotlin
// 测试目标：验证屏幕显示功能
class DisplayTest : BaseTestCase {
    // 测试内容
    - 屏幕显示功能测试
    - 像素点完整性测试
    - 颜色显示准确性测试
    - 亮度调节功能测试

    // 技术指标
    - 坏点数量：< 3个
    - 颜色准确度：色差 ΔE < 5
    - 亮度范围：10-400 nits

    // 判定方式
    - Android设备拍照 + 图像分析
    - 显示纯色检测坏点
}
```

#### 4.2.4 麦克风测试模块
```kotlin
// 测试目标：验证音频录制功能
class MicrophoneTest : BaseTestCase {
    // 测试内容
    - 音频录制功能测试
    - 录音质量测试
    - 噪声抑制效果测试
    - 语音识别准确性测试

    // 技术指标
    - 信噪比：> 40dB
    - 频率响应：100Hz-8kHz
    - 录音延迟：< 50ms

    // 判定方式
    - 分析录制文件信噪比
    - 播放已知频率检测录制结果
}
```

#### 4.2.5 WiFi测试模块
```kotlin
// 测试目标：验证WiFi连接功能
class WiFiTest : BaseTestCase {
    // 测试内容
    - WiFi连接功能测试
    - 信号强度和稳定性测试
    - 数据传输速率测试
    - 多频段支持测试

    // 技术指标
    - 连接成功率：> 95%
    - 2.4G速率：> 10Mbps
    - 5G速率：> 50Mbps
    - 信号强度：> -70dBm

    // 判定方式
    - 成功连接到指定AP
    - 上传/下载速率测试
}
```

#### 4.2.6 蓝牙测试模块
```kotlin
// 测试目标：验证蓝牙连接功能
class BluetoothTest : BaseTestCase {
    // 测试内容
    - 蓝牙连接功能测试
    - 数据传输稳定性测试
    - 连接距离范围测试
    - 多设备连接能力测试

    // 技术指标
    - 连接成功率：> 98%
    - 传输速率：> 1Mbps
    - 有效距离：> 10米
    - 连接延迟：< 3秒

    // 判定方式
    - 配对和连接成功率
    - 数据完整性和速率测试
}
```

#### 4.2.7 5G模块测试
```kotlin
// 测试目标：验证5G网络连接
class FiveGModuleTest : BaseTestCase {
    // 测试内容
    - 5G网络连接测试
    - 信号质量和强度测试
    - 数据传输性能测试
    - 网络切换功能测试

    // 技术指标
    - 注册成功率：> 90%
    - 下载速率：> 100Mbps
    - 上传速率：> 50Mbps
    - RSRP：> -100dBm

    // 判定方式
    - 成功注册到5G网络
    - RSRP、SINR等信号质量指标
}
```

#### 4.2.8 UWB模块测试
```kotlin
// 测试目标：验证UWB信号发射接收
class UWBModuleTest : BaseTestCase {
    // 测试内容
    - UWB信号发射接收测试
    - 距离测量精度测试
    - 定位功能准确性测试
    - 多设备协同工作测试

    // 技术指标
    - 测距精度：±10cm
    - 定位精度：±30cm
    - 更新频率：> 10Hz
    - 有效距离：> 50米

    // 判定方式
    - 与已知距离对比
    - 与GPS坐标对比
}
```

#### 4.2.9 RTK模块测试
```kotlin
// 测试目标：验证GNSS信号接收
class RTKModuleTest : BaseTestCase {
    // 测试内容
    - GNSS信号接收测试
    - RTK差分定位精度测试
    - 首次定位时间测试
    - 多星座支持测试

    // 技术指标
    - 首次定位：< 60秒
    - RTK精度：±2cm
    - 卫星数量：> 8颗
    - 更新频率：1-10Hz

    // 判定方式
    - 可见卫星数量和质量
    - 与已知坐标对比
}
```

### 4.3 测试指令格式设计

#### 4.3.1 通用测试指令结构
```json
{
  "testId": "test_20231201_001",
  "testType": "rgb_light|speaker|display|microphone|wifi|bluetooth|5g_module|uwb_module|rtk_module",
  "timestamp": 1640995200000,
  "parameters": {
    "timeout": 30000,
    "retries": 3,
    "autoJudge": true
  },
  "commands": [
    // 具体测试指令数组
  ]
}
```

#### 4.3.2 各模块测试指令示例

##### RGB灯光测试指令
```json
{
  "testType": "rgb_light",
  "commands": [
    {"action": "set_color", "rgb": [255, 0, 0], "brightness": 100, "duration": 2000},
    {"action": "set_pattern", "pattern": "rainbow", "speed": 50, "duration": 5000},
    {"action": "set_zone", "zone": "head", "rgb": [0, 255, 0], "duration": 2000},
    {"action": "brightness_sweep", "start": 0, "end": 100, "step": 10, "interval": 500}
  ]
}
```

##### 喇叭测试指令
```json
{
  "testType": "speaker",
  "commands": [
    {"action": "play_tone", "frequency": 1000, "volume": 80, "duration": 2000},
    {"action": "play_sweep", "freq_start": 100, "freq_end": 8000, "duration": 5000},
    {"action": "play_file", "file": "test_audio.wav", "volume": 50},
    {"action": "volume_test", "levels": [20, 40, 60, 80, 100], "tone": 1000}
  ]
}
```

##### WiFi测试指令
```json
{
  "testType": "wifi",
  "commands": [
    {"action": "scan_networks", "timeout": 10000},
    {"action": "connect", "ssid": "TEST_AP", "password": "test123", "timeout": 15000},
    {"action": "speed_test", "duration": 30000, "direction": "both", "server": "iperf3"},
    {"action": "signal_monitor", "interval": 1000, "duration": 10000}
  ]
}
```

##### 5G模块测试指令
```json
{
  "testType": "5g_module",
  "commands": [
    {"action": "network_scan", "timeout": 30000},
    {"action": "register_network", "operator": "auto", "timeout": 60000},
    {"action": "speed_test", "test_server": "speedtest.net", "duration": 60000},
    {"action": "signal_monitor", "interval": 5000, "duration": 30000}
  ]
}
```

### 4.4 测试结果数据结构

#### 4.4.1 通用测试结果格式
```json
{
  "testId": "test_20231201_001",
  "testType": "rgb_light",
  "deviceId": "DOG001",
  "startTime": 1640995200000,
  "endTime": 1640995260000,
  "duration": 60000,
  "status": "PASSED|FAILED|TIMEOUT|ERROR",
  "score": 95.5,
  "results": {
    // 具体测试结果数据
  },
  "errors": [
    {
      "code": "E001",
      "message": "颜色偏差超出阈值",
      "timestamp": 1640995230000
    }
  ]
}
```

#### 4.4.2 各模块结果数据示例

##### RGB灯光测试结果
```json
{
  "results": {
    "colorAccuracy": {
      "red": {"expected": [255, 0, 0], "actual": [252, 3, 1], "deviation": 1.2},
      "green": {"expected": [0, 255, 0], "actual": [2, 253, 1], "deviation": 0.8},
      "blue": {"expected": [0, 0, 255], "actual": [1, 2, 252], "deviation": 1.1}
    },
    "brightnessControl": {
      "levels": [0, 25, 50, 75, 100],
      "actualBrightness": [0, 24, 49, 76, 99],
      "accuracy": 98.5
    },
    "responseTime": {
      "average": 85,
      "max": 120,
      "min": 65
    }
  }
}
```

##### 通信模块测试结果
```json
{
  "results": {
    "connectionTest": {
      "attempts": 10,
      "successes": 9,
      "successRate": 90.0,
      "averageTime": 2.5
    },
    "speedTest": {
      "download": {"speed": 45.2, "unit": "Mbps"},
      "upload": {"speed": 23.8, "unit": "Mbps"}
    },
    "signalQuality": {
      "rssi": -65,
      "snr": 25,
      "quality": "Good"
    }
  }
}
```

### 4.5 测试生命周期管理

```
┌─────────────────────────────────────────────────────────┐
│                    Test Lifecycle                      │
│                                                         │
│  Setup → Execute → Validate → Report → Cleanup         │
│    ↓       ↓         ↓         ↓        ↓              │
│  准备环境  执行测试   验证结果   生成报告  清理资源        │
│                                                         │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│  │ 环境检查 │ │ 指令发送 │ │ 数据分析 │ │ 结果存储 │       │
│  │ 设备准备 │ │ 状态监控 │ │ 阈值比较 │ │ 报告生成 │       │
│  │ 参数配置 │ │ 异常处理 │ │ 自动判定 │ │ 数据上传 │       │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘       │
└─────────────────────────────────────────────────────────┘
```

#### 4.5.1 测试执行策略
```kotlin
// 测试执行模式
enum class TestExecutionMode {
    SEQUENTIAL,    // 串行执行
    PARALLEL,      // 并行执行
    CONDITIONAL,   // 条件执行
    CUSTOM        // 自定义顺序
}

// 测试分组策略
class TestGroupStrategy {
    // 硬件测试组（可并行）
    val hardwareGroup = listOf("rgb_light", "speaker", "display", "microphone")

    // 通信测试组（需串行）
    val communicationGroup = listOf("wifi", "bluetooth", "5g_module")

    // 定位测试组（需特殊环境）
    val positioningGroup = listOf("uwb_module", "rtk_module")
}
```

## 5. 数据流架构

### 5.1 单向数据流

```
UI Events → ViewModels → Use Cases → Repositories → Data Sources
    ↑                                                      ↓
UI State ← ViewModels ← Use Cases ← Repositories ← Data Sources
```

### 5.2 状态管理

```kotlin
// 设备连接状态
sealed class DeviceState {
    object Disconnected : DeviceState()
    object Scanning : DeviceState()
    object Connecting : DeviceState()
    object Connected : DeviceState()
    data class Error(val message: String) : DeviceState()
}

// 测试执行状态
sealed class TestState {
    object Idle : TestState()
    object Running : TestState()
    object Paused : TestState()
    object Completed : TestState()
    data class Failed(val error: String) : TestState()
}
```

## 6. 技术栈选择

### 6.1 核心技术
- **开发语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Clean Architecture
- **异步处理**: Kotlin Coroutines + Flow
- **依赖注入**: Hilt
- **网络通信**: Retrofit + OkHttp
- **数据库**: Room
- **蓝牙**: Android Bluetooth API
- **WiFi**: Android WiFi API

### 6.2 第三方库
```gradle
// 网络通信
implementation 'com.squareup.retrofit2:retrofit:2.9.0'
implementation 'com.squareup.okhttp3:okhttp:4.10.0'

// JSON解析
implementation 'com.squareup.moshi:moshi-kotlin:1.14.0'

// 图表显示
implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

// 权限处理
implementation 'com.karumi:dexter:6.2.3'

// 日志
implementation 'com.jakewharton.timber:timber:5.0.1'
```

## 7. 安全和错误处理

### 7.1 安全机制
- 设备认证：MAC地址 + 序列号验证
- 通信加密：TLS 1.3 + AES-256
- 权限管理：最小权限原则
- 数据保护：敏感数据加密存储

### 7.2 错误处理策略
- 网络错误：指数退避重试
- 设备错误：自动重连机制
- 测试错误：优雅降级
- 系统错误：崩溃恢复

这个技术架构确保了系统的高可用性、可扩展性和可维护性。
