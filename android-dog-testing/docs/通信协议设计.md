# 通信协议设计

## 1. 协议概述

### 1.1 通信流程
```
1. 蓝牙扫描发现设备
2. BLE连接获取WiFi配置
3. 切换到WiFi连接
4. HTTP/WebSocket通信执行测试
5. 收集结果并断开连接
```

### 1.2 协议栈
```
┌─────────────────┐
│   应用层协议     │ ← 测试指令、数据格式
├─────────────────┤
│   传输层协议     │ ← HTTP/WebSocket/TCP
├─────────────────┤
│   网络层协议     │ ← IP (局域网)
├─────────────────┤
│  数据链路层协议  │ ← WiFi 802.11 / BLE
├─────────────────┤
│   物理层协议     │ ← 2.4G/5G 无线
└─────────────────┘
```

## 2. 蓝牙BLE协议

### 2.1 GATT服务定义

#### 主服务 (Primary Service)
```
Service UUID: 6E400001-B5A3-F393-E0A9-E50E24DCCA9E
Service Name: Dog Testing Service
```

#### 特征值 (Characteristics)

##### 设备信息特征值 (只读)
```
Characteristic UUID: 6E400002-B5A3-F393-E0A9-E50E24DCCA9E
Properties: READ
Data Format: JSON
Max Length: 512 bytes

示例数据:
{
  "deviceId": "DOG001",
  "model": "QuadrupedRobot-V2",
  "serialNumber": "SN123456789",
  "firmwareVersion": "2.1.0",
  "hardwareVersion": "1.0",
  "manufacturer": "RoboticsInc",
  "batteryLevel": 85,
  "status": "ready"
}
```

##### WiFi配置特征值 (只写)
```
Characteristic UUID: 6E400003-B5A3-F393-E0A9-E50E24DCCA9E
Properties: WRITE
Data Format: JSON
Max Length: 256 bytes

示例数据:
{
  "ssid": "DOG_AP_001",
  "password": "test123456",
  "security": "WPA2",
  "channel": 6,
  "ipAddress": "***********",
  "port": 8080
}
```

##### 状态通知特征值 (通知)
```
Characteristic UUID: 6E400004-B5A3-F393-E0A9-E50E24DCCA9E
Properties: NOTIFY
Data Format: JSON
Max Length: 128 bytes

示例数据:
{
  "timestamp": 1640995200000,
  "status": "wifi_ready",
  "message": "WiFi AP已启动",
  "code": 200
}
```

### 2.2 BLE通信流程

```kotlin
// 1. 扫描设备
fun scanForDevices() {
    bluetoothLeScanner.startScan(
        listOf(ScanFilter.Builder()
            .setServiceUuid(ParcelUuid.fromString(DOG_SERVICE_UUID))
            .build()),
        scanSettings,
        scanCallback
    )
}

// 2. 连接设备
fun connectToDevice(device: BluetoothDevice) {
    gatt = device.connectGatt(context, false, gattCallback)
}

// 3. 读取设备信息
fun readDeviceInfo() {
    val characteristic = gatt?.getService(DOG_SERVICE_UUID)
        ?.getCharacteristic(DEVICE_INFO_UUID)
    gatt?.readCharacteristic(characteristic)
}

// 4. 配置WiFi
fun configureWiFi(config: WiFiConfig) {
    val characteristic = gatt?.getService(DOG_SERVICE_UUID)
        ?.getCharacteristic(WIFI_CONFIG_UUID)
    characteristic?.value = config.toJson().toByteArray()
    gatt?.writeCharacteristic(characteristic)
}
```

## 3. WiFi HTTP协议

### 3.1 RESTful API设计

#### 基础URL
```
Base URL: http://***********:8080/api/v1
Content-Type: application/json
```

#### API端点

##### 设备信息
```
GET /device/info
Response:
{
  "deviceId": "DOG001",
  "model": "QuadrupedRobot-V2",
  "status": "ready",
  "capabilities": ["motor", "sensor", "camera", "audio"],
  "timestamp": 1640995200000
}
```

##### 测试管理
```
POST /test/start
Request:
{
  "testSuite": "production_test",
  "testCases": ["rgb_light", "speaker", "display", "microphone", "wifi", "bluetooth", "5g_module", "uwb_module", "rtk_module"],
  "parameters": {
    "timeout": 300,
    "retries": 3,
    "executionMode": "sequential",
    "autoJudge": true
  }
}

Response:
{
  "testId": "test_20231201_001",
  "status": "started",
  "estimatedDuration": 480,
  "timestamp": 1640995200000,
  "testCases": [
    {
      "testType": "rgb_light",
      "status": "pending",
      "estimatedDuration": 30
    },
    {
      "testType": "speaker",
      "status": "pending",
      "estimatedDuration": 45
    }
  ]
}
```

```
POST /test/execute/{testType}
Request:
{
  "testId": "test_20231201_001",
  "testType": "rgb_light",
  "commands": [
    {"action": "set_color", "rgb": [255, 0, 0], "brightness": 100, "duration": 2000},
    {"action": "set_pattern", "pattern": "rainbow", "speed": 50, "duration": 5000}
  ],
  "parameters": {
    "timeout": 30000,
    "autoJudge": true
  }
}

Response:
{
  "testId": "test_20231201_001",
  "testType": "rgb_light",
  "status": "executing",
  "startTime": 1640995200000,
  "commands": [
    {
      "commandId": "cmd_001",
      "action": "set_color",
      "status": "pending"
    }
  ]
}
```

```
GET /test/status/{testId}
Response:
{
  "testId": "test_20231201_001",
  "status": "running",
  "progress": 45,
  "currentTest": "sensor_test",
  "completedTests": ["motor_test"],
  "failedTests": [],
  "timestamp": 1640995200000
}
```

```
GET /test/result/{testId}
Response:
{
  "testId": "test_20231201_001",
  "status": "completed",
  "overallResult": "pass",
  "totalTests": 10,
  "passedTests": 9,
  "failedTests": 1,
  "duration": 175,
  "results": [
    {
      "testCase": "motor_test",
      "result": "pass",
      "duration": 30,
      "details": {
        "leftFrontMotor": "pass",
        "rightFrontMotor": "pass",
        "leftRearMotor": "pass",
        "rightRearMotor": "pass"
      }
    }
  ],
  "timestamp": 1640995200000
}
```

##### 各测试模块专用API

###### RGB灯光测试API
```
POST /test/rgb-light/control
Request:
{
  "action": "set_color|set_pattern|set_brightness|set_zone",
  "rgb": [255, 0, 0],
  "brightness": 100,
  "pattern": "rainbow|blink|fade",
  "zone": "head|body|tail|all",
  "duration": 2000
}

GET /test/rgb-light/status
Response:
{
  "currentColor": [255, 0, 0],
  "brightness": 85,
  "pattern": "solid",
  "zones": {
    "head": {"rgb": [255, 0, 0], "brightness": 85},
    "body": {"rgb": [0, 255, 0], "brightness": 90}
  }
}
```

###### 音频测试API
```
POST /test/speaker/play
Request:
{
  "action": "play_tone|play_sweep|play_file",
  "frequency": 1000,
  "volume": 80,
  "duration": 2000,
  "file": "test_audio.wav"
}

POST /test/microphone/record
Request:
{
  "duration": 5000,
  "sampleRate": 44100,
  "format": "wav|mp3"
}

GET /test/audio/analysis
Response:
{
  "speaker": {
    "volume": 78.5,
    "frequency": 1000,
    "distortion": 2.1
  },
  "microphone": {
    "snr": 42.3,
    "sensitivity": -38,
    "recordingQuality": "good"
  }
}
```

###### 显示屏测试API
```
POST /test/display/show
Request:
{
  "action": "show_color|show_pattern|show_text|show_image",
  "color": "#FF0000",
  "pattern": "checkerboard|gradient|grid",
  "text": "TEST DISPLAY",
  "fontSize": 24,
  "brightness": 80
}

GET /test/display/status
Response:
{
  "resolution": {"width": 1920, "height": 1080},
  "brightness": 80,
  "currentContent": "color",
  "pixelTest": {
    "deadPixels": 0,
    "stuckPixels": 1,
    "totalPixels": 2073600
  }
}
```

###### 通信模块测试API
```
POST /test/wifi/connect
Request:
{
  "ssid": "TEST_AP",
  "password": "test123",
  "security": "WPA2",
  "timeout": 15000
}

POST /test/bluetooth/scan
Request:
{
  "duration": 10000,
  "deviceType": "all|phone|computer"
}

POST /test/5g/register
Request:
{
  "operator": "auto|manual",
  "apn": "internet",
  "timeout": 60000
}

GET /test/communication/status
Response:
{
  "wifi": {
    "connected": true,
    "ssid": "TEST_AP",
    "signalStrength": -45,
    "speed": {"download": 45.2, "upload": 23.8}
  },
  "bluetooth": {
    "enabled": true,
    "connectedDevices": 2,
    "discoverable": false
  },
  "5g": {
    "registered": true,
    "operator": "China Mobile",
    "signalQuality": {"rsrp": -85, "sinr": 15}
  }
}
```

###### 定位模块测试API
```
POST /test/uwb/ranging
Request:
{
  "targetDevice": "anchor_01",
  "samples": 100,
  "interval": 100
}

POST /test/rtk/position
Request:
{
  "baseStation": "192.168.1.100:2101",
  "duration": 300000,
  "interval": 1000
}

GET /test/positioning/status
Response:
{
  "uwb": {
    "ranging": true,
    "distance": 2.45,
    "accuracy": 0.08,
    "targetDevices": ["anchor_01", "anchor_02"]
  },
  "rtk": {
    "fixed": true,
    "position": {"lat": 39.9042, "lon": 116.4074, "alt": 45.2},
    "accuracy": {"horizontal": 0.02, "vertical": 0.03},
    "satellites": 12
  }
}
```

### 3.2 WebSocket实时通信

#### 连接端点
```
WebSocket URL: ws://***********:8080/ws
```

#### 消息格式
```json
{
  "type": "message_type",
  "timestamp": 1640995200000,
  "data": {}
}
```

#### 消息类型

##### 测试进度监控
```json
{
  "type": "test_progress",
  "timestamp": 1640995200000,
  "data": {
    "testId": "test_20231201_001",
    "progress": 45,
    "currentTest": "sensor_test",
    "message": "正在测试IMU传感器"
  }
}
```

##### 设备状态监控
```json
{
  "type": "device_status",
  "timestamp": 1640995200000,
  "data": {
    "batteryLevel": 75,
    "temperature": 45.2,
    "cpuUsage": 35,
    "memoryUsage": 60,
    "networkSignal": -45
  }
}
```

##### 实时日志流
```json
{
  "type": "log_message",
  "timestamp": 1640995200000,
  "data": {
    "level": "INFO",
    "module": "MotorController",
    "message": "电机测试完成，所有电机运行正常"
  }
}
```

## 4. 数据格式定义

### 4.1 测试用例格式
```json
{
  "testCase": {
    "id": "motor_test_001",
    "name": "电机功能测试",
    "description": "测试所有电机的基本功能",
    "category": "hardware",
    "priority": "high",
    "timeout": 60,
    "retries": 3,
    "parameters": {
      "speed": 50,
      "duration": 10,
      "direction": "all"
    },
    "expectedResults": {
      "motorStatus": "running",
      "currentDraw": {"min": 0.5, "max": 2.0},
      "temperature": {"max": 60}
    }
  }
}
```

### 4.2 测试结果格式
```json
{
  "testResult": {
    "testCaseId": "motor_test_001",
    "result": "pass",
    "startTime": 1640995200000,
    "endTime": 1640995260000,
    "duration": 60,
    "details": {
      "leftFrontMotor": {
        "status": "pass",
        "currentDraw": 1.2,
        "temperature": 42.5,
        "rpm": 1500
      },
      "rightFrontMotor": {
        "status": "pass",
        "currentDraw": 1.1,
        "temperature": 41.8,
        "rpm": 1520
      }
    },
    "logs": [
      {
        "timestamp": 1640995210000,
        "level": "INFO",
        "message": "开始电机测试"
      }
    ],
    "metrics": {
      "averageCurrentDraw": 1.15,
      "maxTemperature": 42.5,
      "averageRpm": 1510
    }
  }
}
```

## 5. 错误处理

### 5.1 HTTP错误码
```
200 OK - 请求成功
400 Bad Request - 请求参数错误
401 Unauthorized - 未授权访问
404 Not Found - 资源不存在
408 Request Timeout - 请求超时
500 Internal Server Error - 服务器内部错误
503 Service Unavailable - 服务不可用
```

### 5.2 自定义错误码
```json
{
  "error": {
    "code": 1001,
    "message": "设备未准备就绪",
    "details": "电池电量不足，请充电后重试",
    "timestamp": 1640995200000
  }
}
```

### 5.3 错误码定义
```
1000-1099: 设备相关错误
1100-1199: 通信相关错误
1200-1299: 测试相关错误
1300-1399: 系统相关错误
```

## 6. 安全机制

### 6.1 设备认证
```json
{
  "auth": {
    "deviceId": "DOG001",
    "serialNumber": "SN123456789",
    "signature": "SHA256_HASH",
    "timestamp": 1640995200000
  }
}
```

### 6.2 数据加密
- BLE通信：AES-128加密
- WiFi通信：TLS 1.3加密
- 敏感数据：AES-256加密存储

这个通信协议设计确保了Android测试端与机器狗设备之间的可靠、安全、高效的数据交换。
