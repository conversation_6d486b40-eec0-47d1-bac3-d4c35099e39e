# Android机器狗产测系统

## 项目概述

本项目是一个基于Android平台的机器狗设备产测系统，通过蓝牙BLE和WiFi AP连接实现自动化的设备测试和验证功能。

### 核心功能
- 🔍 **自动设备发现**：通过蓝牙BLE扫描和连接机器狗设备
- 📡 **双重通信模式**：蓝牙配对 + WiFi局域网数据传输
- 🧪 **全面测试覆盖**：硬件、软件、通信、性能等多维度测试
- 📊 **实时监控**：WebSocket实时进度监控和状态反馈
- 📋 **详细报告**：自动生成测试报告和结果分析

### 技术特点
- **现代化架构**：Clean Architecture + MVVM + Jetpack Compose
- **高性能通信**：异步协程 + 多协议支持
- **可扩展设计**：模块化架构 + 依赖注入
- **用户友好**：Material Design 3 + 直观操作流程

## 项目结构

```
android-dog-testing/
├── docs/                          # 项目文档
│   ├── 技术架构设计.md              # 完整技术架构设计
│   ├── 通信协议设计.md              # 蓝牙和WiFi通信协议
│   ├── 技术栈总结.md                # 技术栈和依赖配置
│   ├── 架构图表.md                  # 系统架构可视化图表
│   └── 业务流程图.md                # 详细业务流程图
├── app/                           # Android应用主模块
│   ├── src/main/java/com/robotics/dogtest/
│   │   ├── ui/                    # 用户界面层
│   │   │   ├── screens/           # 各个页面组件
│   │   │   ├── components/        # 可复用UI组件
│   │   │   └── theme/             # 主题和样式
│   │   ├── data/                  # 数据层
│   │   │   ├── repository/        # 仓库实现
│   │   │   ├── datasource/        # 数据源
│   │   │   └── database/          # 本地数据库
│   │   ├── domain/                # 业务逻辑层
│   │   │   ├── usecase/           # 用例
│   │   │   ├── repository/        # 仓库接口
│   │   │   └── model/             # 领域模型
│   │   ├── infrastructure/        # 基础设施层
│   │   │   ├── bluetooth/         # 蓝牙管理
│   │   │   ├── wifi/              # WiFi管理
│   │   │   ├── network/           # 网络通信
│   │   │   └── testing/           # 测试引擎
│   │   └── di/                    # 依赖注入配置
│   └── build.gradle               # 应用构建配置
├── protocols/                     # 通信协议定义
├── build.gradle                   # 项目构建配置
└── README.md                      # 项目说明文档
```

## 文档导航

### 📚 核心设计文档
1. **[技术架构设计](docs/技术架构设计.md)**
   - 整体系统架构
   - Android应用分层架构
   - 核心模块设计
   - 安全和错误处理

2. **[通信协议设计](docs/通信协议设计.md)**
   - 蓝牙BLE协议定义
   - WiFi HTTP/WebSocket协议
   - 数据格式规范
   - 错误处理机制

3. **[技术栈总结](docs/技术栈总结.md)**
   - 完整技术栈选择
   - 项目依赖配置
   - 权限和安全配置
   - 性能优化设置

### 📊 可视化图表
4. **[架构图表](docs/架构图表.md)**
   - 系统整体架构图
   - 数据流架构图
   - 测试引擎架构图
   - 通信协议栈图
   - 网络拓扑图
   - 状态机图
   - 模块依赖关系图

5. **[业务流程图](docs/业务流程图.md)**
   - 完整测试流程图
   - 设备连接流程图
   - 测试执行流程图
   - 错误处理流程图
   - 数据同步流程图
   - 用户界面导航流程图

## 快速开始

### 环境要求
- Android Studio Hedgehog | 2023.1.1+
- Kotlin 1.9.0+
- Android SDK 34
- 最低支持 Android 7.0 (API 24)

### 开发环境配置
1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 同步Gradle依赖
4. 连接Android设备或启动模拟器
5. 运行应用

### 权限要求
应用需要以下权限：
- 蓝牙相关权限（扫描、连接）
- WiFi相关权限（连接、状态管理）
- 位置权限（蓝牙扫描需要）
- 网络权限（HTTP通信）
- 存储权限（保存测试结果）

## 系统架构概览

### 分层架构
```
┌─────────────────────────────────────────────────────────┐
│                 Presentation Layer                      │
│           Jetpack Compose + ViewModels                  │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   Domain Layer                          │
│              Use Cases + Repository                     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                    Data Layer                           │
│         Repository Impl + Data Sources                  │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│               Infrastructure Layer                      │
│      Bluetooth + WiFi + Network + Testing               │
└─────────────────────────────────────────────────────────┘
```

### 通信流程
1. **蓝牙发现阶段**：扫描并连接机器狗设备
2. **信息交换阶段**：获取设备信息，配置WiFi参数
3. **网络切换阶段**：断开蓝牙，连接WiFi AP
4. **测试执行阶段**：HTTP/WebSocket通信执行测试
5. **结果收集阶段**：收集测试结果，生成报告

## 核心特性

### 🔗 双重通信机制
- **蓝牙BLE**：设备发现、配对、基础信息交换
- **WiFi AP**：高速数据传输、实时测试监控

### 🧪 全面测试覆盖
- **硬件测试**：电机、传感器、电池、音频、摄像头
- **通信测试**：蓝牙、WiFi、网络性能、协议兼容性
- **性能测试**：CPU、内存、存储、响应时间
- **稳定性测试**：压力测试、耐久性测试、可靠性验证

### 📊 实时监控
- **进度监控**：WebSocket实时推送测试进度
- **状态监控**：设备状态、网络状态、系统状态
- **日志流**：实时日志流，便于问题诊断

### 📋 智能报告
- **自动生成**：测试完成后自动生成详细报告
- **多格式支持**：PDF、HTML、JSON等多种格式
- **数据分析**：统计分析、趋势图表、异常检测

## 开发计划

### 阶段一：基础架构 (5天)
- [x] 项目初始化和架构设计
- [ ] 基础模块搭建
- [ ] 依赖注入配置
- [ ] 基础UI框架

### 阶段二：通信模块 (5天)
- [ ] 蓝牙BLE通信实现
- [ ] WiFi连接管理
- [ ] HTTP/WebSocket客户端
- [ ] 通信协议实现

### 阶段三：测试引擎 (5天)
- [ ] 测试引擎核心
- [ ] 测试用例框架
- [ ] 结果验证器
- [ ] 报告生成器

### 阶段四：用户界面 (3天)
- [ ] 主要页面实现
- [ ] 用户交互逻辑
- [ ] 状态管理
- [ ] 错误处理

### 阶段五：集成测试 (2天)
- [ ] 端到端测试
- [ ] 性能优化
- [ ] 错误修复
- [ ] 文档完善

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues：提交技术问题和功能建议
- 邮箱：[项目邮箱]
- 文档：查看docs目录下的详细技术文档

---

**注意**：本项目仍在开发中，部分功能可能尚未完全实现。请参考项目Issues了解最新开发进度。
