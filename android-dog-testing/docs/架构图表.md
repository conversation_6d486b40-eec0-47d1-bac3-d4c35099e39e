# 架构图表集合

## 1. 系统整体架构图

```mermaid
graph TB
    subgraph "Android测试端"
        subgraph "Presentation Layer"
            UI[Compose UI]
            VM[ViewModels]
            NAV[Navigation]
        end
        
        subgraph "Domain Layer"
            UC[Use Cases]
            REPO[Repository Interfaces]
            MODEL[Domain Models]
        end
        
        subgraph "Data Layer"
            IMPL[Repository Impl]
            DS[Data Sources]
            DB[(Room Database)]
        end
        
        subgraph "Infrastructure Layer"
            BT[Bluetooth Manager]
            WIFI[WiFi Manager]
            NET[Network Client]
            TEST[Test Engine]
        end
    end
    
    subgraph "机器狗设备端"
        DOG[机器狗设备]
        AP[WiFi AP热点]
        BLE[蓝牙BLE]
        API[REST API服务]
    end
    
    subgraph "通信协议"
        GATT[BLE GATT协议]
        HTTP[HTTP/WebSocket]
        TCP[TCP/UDP]
    end
    
    UI --> VM
    VM --> UC
    UC --> REPO
    REPO --> IMPL
    IMPL --> DS
    DS --> DB
    
    BT --> GATT
    WIFI --> HTTP
    NET --> TCP
    TEST --> API
    
    GATT --> BLE
    HTTP --> AP
    TCP --> DOG
    
    BLE --> DOG
    AP --> DOG
    API --> DOG
    
    style UI fill:#e1f5fe
    style VM fill:#e8f5e8
    style UC fill:#fff3e0
    style TEST fill:#fce4ec
    style DOG fill:#f3e5f5
```

## 2. 通信流程时序图

```mermaid
sequenceDiagram
    participant A as Android测试端
    participant B as 蓝牙BLE
    participant D as 机器狗设备
    participant W as WiFi AP
    participant H as HTTP服务

    Note over A,H: 阶段1: 设备发现和配对
    A->>B: 开始BLE扫描
    B->>D: 广播设备信息
    D->>B: 返回设备广告包
    B->>A: 发现设备
    A->>B: 连接BLE设备
    B->>D: 建立GATT连接
    
    Note over A,H: 阶段2: 设备信息获取
    A->>D: 读取设备信息特征值
    D->>A: 返回设备详细信息
    A->>D: 写入WiFi配置
    D->>A: 确认配置接收
    
    Note over A,H: 阶段3: WiFi连接切换
    A->>B: 断开BLE连接
    D->>W: 启动WiFi AP热点
    A->>W: 连接WiFi AP
    W->>A: 分配IP地址
    
    Note over A,H: 阶段4: HTTP通信建立
    A->>H: GET /device/info
    H->>A: 返回设备状态
    A->>H: WebSocket连接请求
    H->>A: 建立WebSocket连接
    
    Note over A,H: 阶段5: 测试执行
    A->>H: POST /test/start
    H->>A: 返回测试ID
    
    loop 测试执行循环
        H->>A: WebSocket推送测试进度
        A->>H: GET /test/status
        H->>A: 返回当前状态
    end
    
    Note over A,H: 阶段6: 结果收集
    A->>H: GET /test/result
    H->>A: 返回完整测试结果
    A->>H: 关闭WebSocket连接
    A->>W: 断开WiFi连接
```

## 3. 数据流架构图

```mermaid
graph LR
    subgraph "用户交互层"
        UI[用户界面]
        EVENT[用户事件]
    end
    
    subgraph "状态管理层"
        VM[ViewModel]
        STATE[UI状态]
    end
    
    subgraph "业务逻辑层"
        UC1[扫描设备用例]
        UC2[连接设备用例]
        UC3[执行测试用例]
        UC4[生成报告用例]
    end
    
    subgraph "数据访问层"
        REPO1[设备仓库]
        REPO2[测试仓库]
        REPO3[结果仓库]
    end
    
    subgraph "数据源层"
        BLE_DS[蓝牙数据源]
        WIFI_DS[WiFi数据源]
        LOCAL_DS[本地数据源]
        REMOTE_DS[远程数据源]
    end
    
    EVENT --> VM
    VM --> UC1
    VM --> UC2
    VM --> UC3
    VM --> UC4
    
    UC1 --> REPO1
    UC2 --> REPO1
    UC3 --> REPO2
    UC4 --> REPO3
    
    REPO1 --> BLE_DS
    REPO1 --> WIFI_DS
    REPO2 --> REMOTE_DS
    REPO3 --> LOCAL_DS
    
    STATE --> UI
    VM --> STATE
    
    style UI fill:#e3f2fd
    style VM fill:#e8f5e8
    style UC1 fill:#fff3e0
    style REPO1 fill:#fce4ec
    style BLE_DS fill:#f3e5f5
```

## 4. 测试引擎架构图

```mermaid
graph TB
    subgraph "测试引擎核心"
        EXECUTOR[测试执行器]
        SCHEDULER[测试调度器]
        MONITOR[测试监控器]
        VALIDATOR[结果验证器]
        REPORTER[报告生成器]
    end
    
    subgraph "测试用例分类"
        HW[硬件测试]
        COMM[通信测试]
        PERF[性能测试]
        STAB[稳定性测试]
    end
    
    subgraph "测试生命周期"
        SETUP[环境准备]
        EXEC[执行测试]
        VALID[结果验证]
        REPORT[生成报告]
        CLEANUP[资源清理]
    end
    
    subgraph "并发执行策略"
        SERIAL[串行执行]
        PARALLEL[并行执行]
        PRIORITY[优先级调度]
    end
    
    SCHEDULER --> EXECUTOR
    EXECUTOR --> MONITOR
    MONITOR --> VALIDATOR
    VALIDATOR --> REPORTER
    
    EXECUTOR --> HW
    EXECUTOR --> COMM
    EXECUTOR --> PERF
    EXECUTOR --> STAB
    
    SETUP --> EXEC
    EXEC --> VALID
    VALID --> REPORT
    REPORT --> CLEANUP
    
    SCHEDULER --> SERIAL
    SCHEDULER --> PARALLEL
    SCHEDULER --> PRIORITY
    
    style EXECUTOR fill:#ffeb3b
    style SCHEDULER fill:#4caf50
    style MONITOR fill:#2196f3
    style VALIDATOR fill:#ff9800
    style REPORTER fill:#9c27b0
```

## 5. 蓝牙通信协议栈图

```mermaid
graph TB
    subgraph "Android端协议栈"
        APP1[应用层]
        GATT1[GATT客户端]
        BLE1[BLE协议栈]
        PHY1[物理层]
    end
    
    subgraph "机器狗端协议栈"
        APP2[应用层]
        GATT2[GATT服务端]
        BLE2[BLE协议栈]
        PHY2[物理层]
    end
    
    subgraph "GATT服务定义"
        SERVICE[Dog Testing Service]
        CHAR1[设备信息特征值]
        CHAR2[WiFi配置特征值]
        CHAR3[状态通知特征值]
    end
    
    APP1 <--> GATT1
    GATT1 <--> BLE1
    BLE1 <--> PHY1
    
    APP2 <--> GATT2
    GATT2 <--> BLE2
    BLE2 <--> PHY2
    
    PHY1 <--> PHY2
    
    GATT1 <--> SERVICE
    GATT2 <--> SERVICE
    
    SERVICE --> CHAR1
    SERVICE --> CHAR2
    SERVICE --> CHAR3
    
    style APP1 fill:#e3f2fd
    style APP2 fill:#e8f5e8
    style SERVICE fill:#fff3e0
    style CHAR1 fill:#fce4ec
```

## 6. WiFi网络拓扑图

```mermaid
graph TB
    subgraph "WiFi网络拓扑"
        ANDROID[Android测试端<br/>192.168.4.100]
        AP[机器狗WiFi AP<br/>192.168.4.1]
        DOG[机器狗设备<br/>192.168.4.1]
    end
    
    subgraph "网络服务"
        HTTP[HTTP服务<br/>端口8080]
        WS[WebSocket服务<br/>端口8080]
        TCP[TCP服务<br/>端口9090]
    end
    
    subgraph "通信协议"
        REST[RESTful API]
        REALTIME[实时通信]
        STREAM[数据流传输]
    end
    
    ANDROID -.->|WiFi连接| AP
    AP --> DOG
    
    DOG --> HTTP
    DOG --> WS
    DOG --> TCP
    
    HTTP --> REST
    WS --> REALTIME
    TCP --> STREAM
    
    style ANDROID fill:#e3f2fd
    style AP fill:#4caf50
    style DOG fill:#ff9800
    style HTTP fill:#2196f3
```

## 7. 状态机图

```mermaid
stateDiagram-v2
    [*] --> Idle: 应用启动
    
    Idle --> Scanning: 开始扫描
    Scanning --> DeviceFound: 发现设备
    Scanning --> ScanTimeout: 扫描超时
    ScanTimeout --> Idle: 重新开始
    
    DeviceFound --> Connecting: 选择连接
    Connecting --> Connected: 连接成功
    Connecting --> ConnectFailed: 连接失败
    ConnectFailed --> Scanning: 重新扫描
    
    Connected --> ConfiguringWiFi: 配置WiFi
    ConfiguringWiFi --> WiFiConnecting: 切换WiFi
    WiFiConnecting --> WiFiConnected: WiFi连接成功
    WiFiConnecting --> WiFiFailed: WiFi连接失败
    WiFiFailed --> Connected: 返回蓝牙
    
    WiFiConnected --> TestReady: 准备测试
    TestReady --> TestRunning: 开始测试
    TestRunning --> TestCompleted: 测试完成
    TestRunning --> TestFailed: 测试失败
    TestRunning --> TestPaused: 暂停测试
    TestPaused --> TestRunning: 恢复测试
    
    TestCompleted --> ReportGenerated: 生成报告
    TestFailed --> ReportGenerated: 生成错误报告
    ReportGenerated --> Disconnecting: 断开连接
    Disconnecting --> Idle: 返回初始状态
```

## 8. 模块依赖关系图

```mermaid
graph TB
    subgraph "应用模块"
        APP[app模块]
    end
    
    subgraph "功能模块"
        BLUETOOTH[bluetooth模块]
        WIFI[wifi模块]
        TESTING[testing模块]
        UI[ui模块]
    end
    
    subgraph "数据模块"
        DATA[data模块]
        DATABASE[database模块]
        NETWORK[network模块]
    end
    
    subgraph "核心模块"
        DOMAIN[domain模块]
        COMMON[common模块]
    end
    
    APP --> UI
    APP --> BLUETOOTH
    APP --> WIFI
    APP --> TESTING
    
    UI --> DOMAIN
    BLUETOOTH --> DOMAIN
    WIFI --> DOMAIN
    TESTING --> DOMAIN
    
    DOMAIN --> DATA
    DATA --> DATABASE
    DATA --> NETWORK
    
    BLUETOOTH --> COMMON
    WIFI --> COMMON
    TESTING --> COMMON
    DATA --> COMMON
    
    style APP fill:#e3f2fd
    style DOMAIN fill:#4caf50
    style DATA fill:#ff9800
    style COMMON fill:#9c27b0
```

这些图表提供了系统架构的可视化表示，便于理解各个组件之间的关系和数据流向。
