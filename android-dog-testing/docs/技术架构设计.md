# Android机器狗产测系统 - 技术架构设计

## 1. 整体系统架构

```
┌─────────────────┐    蓝牙BLE     ┌─────────────────┐
│   Android测试端  │ ◄──────────► │   机器狗设备端   │
│   (主控设备)     │              │   (被测设备)     │
└─────────────────┘              └─────────────────┘
         │                                │
         │          WiFi局域网             │
         └────────────────────────────────┘
                    HTTP/WebSocket
```

### 系统组件
- **Android测试端**：测试控制、UI交互、结果分析
- **机器狗设备端**：接受测试指令、执行测试、返回结果
- **通信链路**：蓝牙发现配对 + WiFi数据传输
- **测试协议**：标准化的测试指令和数据格式

## 2. Android应用分层架构

### 2.1 架构模式：Clean Architecture + MVVM

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Compose UI  │  │ ViewModels  │  │ Navigation  │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                     Domain Layer                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  Use Cases  │  │ Repositories│  │   Models    │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                      Data Layer                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Repository  │  │ Data Sources│  │  Database   │     │
│  │    Impl     │  │ Local/Remote│  │    DAOs     │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Bluetooth   │  │    WiFi     │  │   Testing   │     │
│  │  Manager    │  │   Manager   │  │   Engine    │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### 2.2 核心模块设计

#### Presentation Layer (表现层)
```kotlin
// UI组件
- MainActivity
- DeviceListScreen
- TestExecutionScreen  
- TestResultScreen
- SettingsScreen

// ViewModels
- DeviceViewModel
- TestViewModel
- ResultViewModel
- SettingsViewModel
```

#### Domain Layer (业务逻辑层)
```kotlin
// Use Cases
- ScanDevicesUseCase
- ConnectDeviceUseCase
- ExecuteTestUseCase
- GenerateReportUseCase

// Repository接口
- DeviceRepository
- TestRepository
- ResultRepository

// Domain Models
- Device
- TestCase
- TestResult
- TestReport
```

#### Data Layer (数据层)
```kotlin
// Repository实现
- DeviceRepositoryImpl
- TestRepositoryImpl
- ResultRepositoryImpl

// Data Sources
- BluetoothDataSource
- WiFiDataSource
- LocalDataSource
- RemoteDataSource

// Database
- TestDatabase
- DeviceDao
- TestResultDao
```

#### Infrastructure Layer (基础设施层)
```kotlin
// 蓝牙管理
- BluetoothManager
- BleScanner
- GattClient

// WiFi管理
- WiFiManager
- NetworkClient
- HttpClient

// 测试引擎
- TestEngine
- TestExecutor
- TestValidator
```

## 3. 通信协议架构

### 3.1 蓝牙通信协议 (设备发现和配对)

```
应用层：设备信息交换、WiFi配置传输
GATT层：自定义Service和Characteristic
BLE层：低功耗蓝牙协议
物理层：2.4GHz无线通信
```

#### 自定义GATT服务
```
Service UUID: 0000180F-0000-1000-8000-00805F9B34FB
├── Device Info Characteristic (Read)
│   └── 设备型号、序列号、固件版本
├── WiFi Config Characteristic (Write)
│   └── SSID、密码、IP配置
└── Status Characteristic (Notify)
    └── 连接状态、错误信息
```

### 3.2 WiFi通信协议 (测试执行)

```
应用层：测试指令、数据传输、结果回传
传输层：HTTP/HTTPS、WebSocket、TCP
网络层：IP (192.168.4.x 局域网)
数据链路层：WiFi 802.11 b/g/n
物理层：2.4G/5G WiFi
```

#### RESTful API设计
```
GET  /api/device/info          # 获取设备信息
POST /api/test/start           # 开始测试
GET  /api/test/status          # 获取测试状态
GET  /api/test/result/{id}     # 获取测试结果
POST /api/test/stop            # 停止测试
```

#### WebSocket实时通信
```
/ws/test/monitor               # 测试进度监控
/ws/device/status              # 设备状态监控
/ws/logs/stream                # 实时日志流
```

## 4. 测试框架架构

### 4.1 测试引擎设计

```
┌─────────────────────────────────────────────────────────┐
│                    Test Engine                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │Test Executor│  │Test Scheduler│  │Test Reporter│     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │Test Validator│  │Test Monitor │  │Test Logger  │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### 4.2 测试用例分类

#### 硬件功能测试
```kotlin
- MotorTest: 电机功能测试
- SensorTest: 传感器测试
- BatteryTest: 电池测试
- AudioTest: 音频测试
- CameraTest: 摄像头测试
```

#### 通信功能测试
```kotlin
- BluetoothTest: 蓝牙连接测试
- WiFiTest: WiFi连接测试
- NetworkTest: 网络性能测试
- ProtocolTest: 通信协议测试
```

#### 性能测试
```kotlin
- CPUTest: CPU性能测试
- MemoryTest: 内存使用测试
- StorageTest: 存储性能测试
- ResponseTest: 响应时间测试
```

#### 稳定性测试
```kotlin
- StressTest: 压力测试
- EnduranceTest: 耐久性测试
- ReliabilityTest: 可靠性测试
```

### 4.3 测试生命周期

```
Setup → Execute → Validate → Report → Cleanup
  ↓       ↓         ↓         ↓        ↓
准备环境  执行测试   验证结果   生成报告  清理资源
```

## 5. 数据流架构

### 5.1 单向数据流

```
UI Events → ViewModels → Use Cases → Repositories → Data Sources
    ↑                                                      ↓
UI State ← ViewModels ← Use Cases ← Repositories ← Data Sources
```

### 5.2 状态管理

```kotlin
// 设备连接状态
sealed class DeviceState {
    object Disconnected : DeviceState()
    object Scanning : DeviceState()
    object Connecting : DeviceState()
    object Connected : DeviceState()
    data class Error(val message: String) : DeviceState()
}

// 测试执行状态
sealed class TestState {
    object Idle : TestState()
    object Running : TestState()
    object Paused : TestState()
    object Completed : TestState()
    data class Failed(val error: String) : TestState()
}
```

## 6. 技术栈选择

### 6.1 核心技术
- **开发语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Clean Architecture
- **异步处理**: Kotlin Coroutines + Flow
- **依赖注入**: Hilt
- **网络通信**: Retrofit + OkHttp
- **数据库**: Room
- **蓝牙**: Android Bluetooth API
- **WiFi**: Android WiFi API

### 6.2 第三方库
```gradle
// 网络通信
implementation 'com.squareup.retrofit2:retrofit:2.9.0'
implementation 'com.squareup.okhttp3:okhttp:4.10.0'

// JSON解析
implementation 'com.squareup.moshi:moshi-kotlin:1.14.0'

// 图表显示
implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

// 权限处理
implementation 'com.karumi:dexter:6.2.3'

// 日志
implementation 'com.jakewharton.timber:timber:5.0.1'
```

## 7. 安全和错误处理

### 7.1 安全机制
- 设备认证：MAC地址 + 序列号验证
- 通信加密：TLS 1.3 + AES-256
- 权限管理：最小权限原则
- 数据保护：敏感数据加密存储

### 7.2 错误处理策略
- 网络错误：指数退避重试
- 设备错误：自动重连机制
- 测试错误：优雅降级
- 系统错误：崩溃恢复

这个技术架构确保了系统的高可用性、可扩展性和可维护性。
