# 业务流程图集合

## 1. 完整测试流程图

```mermaid
flowchart TD
    START([开始]) --> INIT[初始化应用]
    INIT --> PERM{检查权限}
    PERM -->|缺少权限| REQ_PERM[请求权限]
    REQ_PERM --> PERM
    PERM -->|权限完整| SCAN[开始蓝牙扫描]
    
    SCAN --> FOUND{发现设备?}
    FOUND -->|否| TIMEOUT{扫描超时?}
    TIMEOUT -->|否| SCAN
    TIMEOUT -->|是| ERROR1[显示扫描失败]
    ERROR1 --> END([结束])
    
    FOUND -->|是| SELECT[选择设备]
    SELECT --> CONNECT[建立蓝牙连接]
    CONNECT --> CONN_OK{连接成功?}
    CONN_OK -->|否| ERROR2[连接失败]
    ERROR2 --> SCAN
    
    CONN_OK -->|是| READ_INFO[读取设备信息]
    READ_INFO --> VALIDATE{验证设备?}
    VALIDATE -->|否| ERROR3[设备不匹配]
    ERROR3 --> SCAN
    
    VALIDATE -->|是| CONFIG_WIFI[配置WiFi信息]
    CONFIG_WIFI --> DISCONNECT_BLE[断开蓝牙]
    DISCONNECT_BLE --> CONNECT_WIFI[连接WiFi AP]
    
    CONNECT_WIFI --> WIFI_OK{WiFi连接成功?}
    WIFI_OK -->|否| ERROR4[WiFi连接失败]
    ERROR4 --> CONNECT
    
    WIFI_OK -->|是| HTTP_TEST[测试HTTP连接]
    HTTP_TEST --> HTTP_OK{HTTP通信正常?}
    HTTP_OK -->|否| ERROR5[网络通信失败]
    ERROR5 --> CONNECT_WIFI
    
    HTTP_OK -->|是| SETUP_WS[建立WebSocket]
    SETUP_WS --> SELECT_TESTS[选择测试用例]
    SELECT_TESTS --> START_TEST[开始测试]
    
    START_TEST --> MONITOR[监控测试进度]
    MONITOR --> TEST_DONE{测试完成?}
    TEST_DONE -->|否| MONITOR
    TEST_DONE -->|是| COLLECT[收集测试结果]
    
    COLLECT --> GENERATE[生成测试报告]
    GENERATE --> SAVE[保存结果]
    SAVE --> CLEANUP[清理资源]
    CLEANUP --> SUCCESS[测试成功完成]
    SUCCESS --> END
    
    style START fill:#4caf50
    style END fill:#f44336
    style SUCCESS fill:#2196f3
    style ERROR1 fill:#ff9800
    style ERROR2 fill:#ff9800
    style ERROR3 fill:#ff9800
    style ERROR4 fill:#ff9800
    style ERROR5 fill:#ff9800
```

## 2. 设备连接流程图

```mermaid
flowchart TD
    START([开始连接]) --> CHECK_BT{蓝牙已开启?}
    CHECK_BT -->|否| ENABLE_BT[启用蓝牙]
    ENABLE_BT --> CHECK_BT
    CHECK_BT -->|是| START_SCAN[开始扫描]
    
    START_SCAN --> SCAN_LOOP[扫描循环]
    SCAN_LOOP --> DEVICE_FOUND{发现目标设备?}
    DEVICE_FOUND -->|否| SCAN_TIMEOUT{扫描超时?}
    SCAN_TIMEOUT -->|否| SCAN_LOOP
    SCAN_TIMEOUT -->|是| SCAN_FAILED[扫描失败]
    
    DEVICE_FOUND -->|是| STOP_SCAN[停止扫描]
    STOP_SCAN --> CONNECT_GATT[连接GATT]
    CONNECT_GATT --> GATT_CONNECTED{GATT连接成功?}
    GATT_CONNECTED -->|否| CONNECT_RETRY{重试次数<3?}
    CONNECT_RETRY -->|是| CONNECT_GATT
    CONNECT_RETRY -->|否| CONNECT_FAILED[连接失败]
    
    GATT_CONNECTED -->|是| DISCOVER_SERVICES[发现服务]
    DISCOVER_SERVICES --> SERVICE_FOUND{找到目标服务?}
    SERVICE_FOUND -->|否| SERVICE_ERROR[服务不匹配]
    SERVICE_FOUND -->|是| READ_CHARACTERISTICS[读取特征值]
    
    READ_CHARACTERISTICS --> ENABLE_NOTIFICATIONS[启用通知]
    ENABLE_NOTIFICATIONS --> CONNECTION_READY[连接就绪]
    CONNECTION_READY --> SUCCESS([连接成功])
    
    SCAN_FAILED --> FAILED([连接失败])
    CONNECT_FAILED --> FAILED
    SERVICE_ERROR --> FAILED
    
    style START fill:#4caf50
    style SUCCESS fill:#2196f3
    style FAILED fill:#f44336
    style CONNECTION_READY fill:#ff9800
```

## 3. 测试执行流程图

```mermaid
flowchart TD
    START([开始测试]) --> LOAD_CONFIG[加载测试配置]
    LOAD_CONFIG --> VALIDATE_CONFIG{配置有效?}
    VALIDATE_CONFIG -->|否| CONFIG_ERROR[配置错误]
    CONFIG_ERROR --> END([结束])
    
    VALIDATE_CONFIG -->|是| INIT_ENGINE[初始化测试引擎]
    INIT_ENGINE --> CREATE_SCHEDULE[创建测试调度]
    CREATE_SCHEDULE --> START_EXECUTION[开始执行]
    
    START_EXECUTION --> GET_NEXT_TEST[获取下一个测试]
    GET_NEXT_TEST --> HAS_TEST{还有测试?}
    HAS_TEST -->|否| ALL_DONE[所有测试完成]
    
    HAS_TEST -->|是| CHECK_DEPENDENCY{检查依赖?}
    CHECK_DEPENDENCY -->|未满足| WAIT_DEPENDENCY[等待依赖]
    WAIT_DEPENDENCY --> CHECK_DEPENDENCY
    CHECK_DEPENDENCY -->|已满足| SETUP_TEST[准备测试环境]
    
    SETUP_TEST --> EXECUTE_TEST[执行测试]
    EXECUTE_TEST --> MONITOR_PROGRESS[监控进度]
    MONITOR_PROGRESS --> TEST_COMPLETE{测试完成?}
    TEST_COMPLETE -->|否| CHECK_TIMEOUT{超时?}
    CHECK_TIMEOUT -->|否| MONITOR_PROGRESS
    CHECK_TIMEOUT -->|是| TEST_TIMEOUT[测试超时]
    
    TEST_COMPLETE -->|是| VALIDATE_RESULT[验证结果]
    VALIDATE_RESULT --> RESULT_VALID{结果有效?}
    RESULT_VALID -->|否| TEST_INVALID[结果无效]
    RESULT_VALID -->|是| SAVE_RESULT[保存结果]
    
    SAVE_RESULT --> CLEANUP_TEST[清理测试环境]
    TEST_TIMEOUT --> CLEANUP_TEST
    TEST_INVALID --> CLEANUP_TEST
    CLEANUP_TEST --> GET_NEXT_TEST
    
    ALL_DONE --> GENERATE_REPORT[生成测试报告]
    GENERATE_REPORT --> SAVE_REPORT[保存报告]
    SAVE_REPORT --> CLEANUP_ENGINE[清理测试引擎]
    CLEANUP_ENGINE --> SUCCESS([测试成功])
    
    style START fill:#4caf50
    style SUCCESS fill:#2196f3
    style END fill:#f44336
    style TEST_TIMEOUT fill:#ff9800
    style TEST_INVALID fill:#ff9800
```

## 4. 错误处理流程图

```mermaid
flowchart TD
    ERROR([发生错误]) --> CLASSIFY{错误分类}
    
    CLASSIFY -->|网络错误| NETWORK_ERROR[网络错误处理]
    CLASSIFY -->|设备错误| DEVICE_ERROR[设备错误处理]
    CLASSIFY -->|测试错误| TEST_ERROR[测试错误处理]
    CLASSIFY -->|系统错误| SYSTEM_ERROR[系统错误处理]
    
    NETWORK_ERROR --> NET_RETRY{可重试?}
    NET_RETRY -->|是| RETRY_COUNT{重试次数<3?}
    RETRY_COUNT -->|是| WAIT_BACKOFF[等待退避]
    WAIT_BACKOFF --> RETRY_NETWORK[重试网络操作]
    RETRY_NETWORK --> NET_SUCCESS{重试成功?}
    NET_SUCCESS -->|是| RECOVERY([恢复正常])
    NET_SUCCESS -->|否| NETWORK_ERROR
    RETRY_COUNT -->|否| NET_FAIL[网络失败]
    NET_RETRY -->|否| NET_FAIL
    
    DEVICE_ERROR --> DEV_RECONNECT{可重连?}
    DEV_RECONNECT -->|是| RECONNECT_DEVICE[重新连接设备]
    RECONNECT_DEVICE --> DEV_SUCCESS{重连成功?}
    DEV_SUCCESS -->|是| RECOVERY
    DEV_SUCCESS -->|否| DEV_FAIL[设备失败]
    DEV_RECONNECT -->|否| DEV_FAIL
    
    TEST_ERROR --> TEST_SKIP{可跳过?}
    TEST_SKIP -->|是| SKIP_TEST[跳过当前测试]
    SKIP_TEST --> CONTINUE_NEXT[继续下一个测试]
    CONTINUE_NEXT --> RECOVERY
    TEST_SKIP -->|否| TEST_ABORT[中止测试]
    
    SYSTEM_ERROR --> SYS_CRITICAL{严重错误?}
    SYS_CRITICAL -->|是| CRASH_REPORT[生成崩溃报告]
    CRASH_REPORT --> APP_EXIT[应用退出]
    SYS_CRITICAL -->|否| SYS_RECOVER[系统恢复]
    SYS_RECOVER --> RECOVERY
    
    NET_FAIL --> LOG_ERROR[记录错误日志]
    DEV_FAIL --> LOG_ERROR
    TEST_ABORT --> LOG_ERROR
    LOG_ERROR --> USER_NOTIFY[通知用户]
    USER_NOTIFY --> GRACEFUL_EXIT[优雅退出]
    
    APP_EXIT --> END([应用结束])
    GRACEFUL_EXIT --> END
    
    style ERROR fill:#f44336
    style RECOVERY fill:#4caf50
    style END fill:#9e9e9e
    style CRASH_REPORT fill:#ff5722
```

## 5. 数据同步流程图

```mermaid
flowchart TD
    START([开始同步]) --> CHECK_CONNECTION{检查连接状态}
    CHECK_CONNECTION -->|断开| RECONNECT[重新连接]
    RECONNECT --> CHECK_CONNECTION
    CHECK_CONNECTION -->|连接| GET_LOCAL_DATA[获取本地数据]
    
    GET_LOCAL_DATA --> GET_REMOTE_DATA[获取远程数据]
    GET_REMOTE_DATA --> COMPARE_VERSION[比较版本]
    COMPARE_VERSION --> VERSION_DIFF{版本不同?}
    
    VERSION_DIFF -->|否| NO_SYNC_NEEDED[无需同步]
    NO_SYNC_NEEDED --> SUCCESS([同步完成])
    
    VERSION_DIFF -->|是| CONFLICT_CHECK{存在冲突?}
    CONFLICT_CHECK -->|否| AUTO_MERGE[自动合并]
    AUTO_MERGE --> UPLOAD_CHANGES[上传变更]
    UPLOAD_CHANGES --> UPDATE_LOCAL[更新本地]
    UPDATE_LOCAL --> SUCCESS
    
    CONFLICT_CHECK -->|是| RESOLVE_STRATEGY{解决策略}
    RESOLVE_STRATEGY -->|本地优先| USE_LOCAL[使用本地版本]
    RESOLVE_STRATEGY -->|远程优先| USE_REMOTE[使用远程版本]
    RESOLVE_STRATEGY -->|手动解决| MANUAL_RESOLVE[手动解决冲突]
    
    USE_LOCAL --> UPLOAD_LOCAL[上传本地版本]
    UPLOAD_LOCAL --> SUCCESS
    
    USE_REMOTE --> DOWNLOAD_REMOTE[下载远程版本]
    DOWNLOAD_REMOTE --> SUCCESS
    
    MANUAL_RESOLVE --> USER_INPUT[用户选择]
    USER_INPUT --> APPLY_RESOLUTION[应用解决方案]
    APPLY_RESOLUTION --> SUCCESS
    
    style START fill:#4caf50
    style SUCCESS fill:#2196f3
    style MANUAL_RESOLVE fill:#ff9800
    style CONFLICT_CHECK fill:#f44336
```

## 6. 用户界面导航流程图

```mermaid
flowchart TD
    SPLASH[启动页面] --> MAIN[主界面]
    MAIN --> SCAN_PAGE[设备扫描页面]
    MAIN --> HISTORY[历史记录页面]
    MAIN --> SETTINGS[设置页面]
    
    SCAN_PAGE --> DEVICE_LIST[设备列表]
    DEVICE_LIST --> DEVICE_DETAIL[设备详情页面]
    DEVICE_DETAIL --> CONNECTION[连接页面]
    CONNECTION --> TEST_CONFIG[测试配置页面]
    
    TEST_CONFIG --> TEST_EXECUTION[测试执行页面]
    TEST_EXECUTION --> PROGRESS[进度监控页面]
    PROGRESS --> RESULT[结果页面]
    RESULT --> REPORT[报告页面]
    
    HISTORY --> HISTORY_DETAIL[历史详情页面]
    HISTORY_DETAIL --> REPORT
    
    SETTINGS --> BLUETOOTH_SETTINGS[蓝牙设置]
    SETTINGS --> WIFI_SETTINGS[WiFi设置]
    SETTINGS --> TEST_SETTINGS[测试设置]
    SETTINGS --> ABOUT[关于页面]
    
    REPORT --> SHARE[分享页面]
    REPORT --> EXPORT[导出页面]
    
    %% 返回路径
    DEVICE_LIST -.->|返回| SCAN_PAGE
    DEVICE_DETAIL -.->|返回| DEVICE_LIST
    CONNECTION -.->|返回| DEVICE_DETAIL
    TEST_CONFIG -.->|返回| CONNECTION
    TEST_EXECUTION -.->|返回| TEST_CONFIG
    PROGRESS -.->|返回| TEST_EXECUTION
    RESULT -.->|返回| PROGRESS
    REPORT -.->|返回| RESULT
    
    style SPLASH fill:#e3f2fd
    style MAIN fill:#4caf50
    style TEST_EXECUTION fill:#ff9800
    style RESULT fill:#2196f3
    style REPORT fill:#9c27b0
```

这些业务流程图详细描述了系统的各个关键流程，为开发和测试提供了清晰的指导。
