# 机器狗产测系统 - 测试模块详细规范

## 1. 概述

本文档详细定义了机器狗产测系统中9大核心测试模块的技术规范、实现要求和验收标准。

### 1.1 测试模块清单
1. **RGB灯光测试** - 验证LED灯珠显示功能
2. **喇叭测试** - 验证音频输出功能  
3. **显示屏测试** - 验证屏幕显示功能
4. **麦克风测试** - 验证音频录制功能
5. **WiFi测试** - 验证无线网络功能
6. **蓝牙测试** - 验证蓝牙通信功能
7. **5G模块测试** - 验证5G网络功能
8. **UWB模块测试** - 验证超宽带定位功能
9. **RTK模块测试** - 验证高精度定位功能

### 1.2 测试分类

#### 按复杂度分类
- **简单测试**：RGB灯光、喇叭、显示屏
- **中等复杂度**：麦克风、WiFi、蓝牙
- **高复杂度**：5G、UWB、RTK

#### 按判定方式分类
- **自动判定**：WiFi、蓝牙、5G、UWB、RTK
- **半自动判定**：喇叭、麦克风
- **人工辅助**：RGB灯光、显示屏

#### 按执行策略分类
- **并行执行组**：RGB灯光、显示屏
- **串行执行组**：WiFi、蓝牙、5G
- **独立执行组**：UWB、RTK

## 2. RGB灯光测试模块

### 2.1 测试目标
验证机器狗RGB LED灯珠的完整功能，包括颜色准确性、亮度控制、动态效果和响应时间。

### 2.2 硬件要求
- RGB LED灯珠数量：≥4个（头部、身体、尾部等位置）
- 颜色深度：24位真彩色（RGB各8位）
- 亮度控制：0-100%连续可调
- 响应时间：≤100ms

### 2.3 测试用例设计

#### 2.3.1 基础颜色测试
```json
{
  "testCase": "basic_colors",
  "description": "测试基础颜色显示准确性",
  "commands": [
    {"action": "set_color", "rgb": [255, 0, 0], "duration": 2000},
    {"action": "set_color", "rgb": [0, 255, 0], "duration": 2000},
    {"action": "set_color", "rgb": [0, 0, 255], "duration": 2000},
    {"action": "set_color", "rgb": [255, 255, 255], "duration": 2000},
    {"action": "set_color", "rgb": [0, 0, 0], "duration": 2000}
  ],
  "expectedResults": {
    "colorAccuracy": "±5%",
    "responseTime": "≤100ms"
  }
}
```

#### 2.3.2 亮度控制测试
```json
{
  "testCase": "brightness_control",
  "description": "测试亮度调节功能",
  "commands": [
    {"action": "set_brightness", "level": 0, "color": [255, 255, 255], "duration": 1000},
    {"action": "set_brightness", "level": 25, "color": [255, 255, 255], "duration": 1000},
    {"action": "set_brightness", "level": 50, "color": [255, 255, 255], "duration": 1000},
    {"action": "set_brightness", "level": 75, "color": [255, 255, 255], "duration": 1000},
    {"action": "set_brightness", "level": 100, "color": [255, 255, 255], "duration": 1000}
  ],
  "expectedResults": {
    "brightnessAccuracy": "±3%",
    "linearResponse": true
  }
}
```

#### 2.3.3 动态效果测试
```json
{
  "testCase": "dynamic_effects",
  "description": "测试动态灯光效果",
  "commands": [
    {"action": "set_pattern", "pattern": "rainbow", "speed": 50, "duration": 5000},
    {"action": "set_pattern", "pattern": "blink", "color": [255, 0, 0], "frequency": 2, "duration": 3000},
    {"action": "set_pattern", "pattern": "fade", "color": [0, 255, 0], "speed": 30, "duration": 4000}
  ],
  "expectedResults": {
    "patternSmooth": true,
    "timingAccuracy": "±50ms"
  }
}
```

### 2.4 判定标准
- **颜色准确度**：RGB值偏差 < 5%
- **亮度控制**：亮度偏差 < 3%
- **响应时间**：指令到显示变化 < 100ms
- **同步性**：多个灯珠同步误差 < 20ms

### 2.5 检测方法
- **自动检测**：Android设备摄像头 + OpenCV图像分析
- **人工确认**：测试人员目视检查 + 手动标记
- **数据记录**：RGB值、亮度值、响应时间

## 3. 喇叭测试模块

### 3.1 测试目标
验证机器狗音频输出系统的完整功能，包括音量控制、频率响应、音质和失真度。

### 3.2 硬件要求
- 喇叭功率：≥5W
- 频率响应：100Hz - 8kHz
- 音量范围：40-85dB
- 总谐波失真：≤5%

### 3.3 测试用例设计

#### 3.3.1 音量控制测试
```json
{
  "testCase": "volume_control",
  "description": "测试音量调节功能",
  "commands": [
    {"action": "play_tone", "frequency": 1000, "volume": 20, "duration": 2000},
    {"action": "play_tone", "frequency": 1000, "volume": 40, "duration": 2000},
    {"action": "play_tone", "frequency": 1000, "volume": 60, "duration": 2000},
    {"action": "play_tone", "frequency": 1000, "volume": 80, "duration": 2000},
    {"action": "play_tone", "frequency": 1000, "volume": 100, "duration": 2000}
  ],
  "expectedResults": {
    "volumeRange": "40-85dB",
    "volumeAccuracy": "±3dB"
  }
}
```

#### 3.3.2 频率响应测试
```json
{
  "testCase": "frequency_response",
  "description": "测试频率响应特性",
  "commands": [
    {"action": "play_sweep", "freq_start": 100, "freq_end": 8000, "duration": 10000, "volume": 70},
    {"action": "play_tone", "frequency": 100, "volume": 70, "duration": 2000},
    {"action": "play_tone", "frequency": 1000, "volume": 70, "duration": 2000},
    {"action": "play_tone", "frequency": 4000, "volume": 70, "duration": 2000},
    {"action": "play_tone", "frequency": 8000, "volume": 70, "duration": 2000}
  ],
  "expectedResults": {
    "frequencyRange": "100Hz-8kHz",
    "responseVariation": "±3dB"
  }
}
```

### 3.4 判定标准
- **音量范围**：40-85dB可调
- **频率响应**：100Hz-8kHz ±3dB
- **总谐波失真**：< 5%
- **响应时间**：播放指令到声音输出 < 200ms

### 3.5 检测方法
- **音量测量**：Android设备麦克风 + 分贝计算
- **频谱分析**：FFT频谱分析算法
- **失真测量**：谐波分析算法

## 4. 显示屏测试模块

### 4.1 测试目标
验证机器狗显示屏的完整功能，包括像素完整性、颜色准确性、亮度控制和显示质量。

### 4.2 硬件要求
- 屏幕类型：LCD/OLED
- 分辨率：≥480x320
- 颜色深度：24位真彩色
- 亮度范围：10-400 nits
- 坏点标准：≤3个

### 4.3 测试用例设计

#### 4.3.1 像素完整性测试
```json
{
  "testCase": "pixel_integrity",
  "description": "检测屏幕坏点和亮点",
  "commands": [
    {"action": "show_color", "color": "#000000", "duration": 3000},
    {"action": "show_color", "color": "#FFFFFF", "duration": 3000},
    {"action": "show_color", "color": "#FF0000", "duration": 2000},
    {"action": "show_color", "color": "#00FF00", "duration": 2000},
    {"action": "show_color", "color": "#0000FF", "duration": 2000}
  ],
  "expectedResults": {
    "deadPixels": "≤3",
    "stuckPixels": "≤3"
  }
}
```

#### 4.3.2 显示质量测试
```json
{
  "testCase": "display_quality",
  "description": "测试显示质量和颜色准确性",
  "commands": [
    {"action": "show_pattern", "pattern": "checkerboard", "size": 8, "duration": 3000},
    {"action": "show_pattern", "pattern": "gradient", "direction": "horizontal", "duration": 3000},
    {"action": "show_text", "text": "DISPLAY TEST 123", "fontSize": 24, "duration": 3000}
  ],
  "expectedResults": {
    "colorAccuracy": "ΔE < 5",
    "textClarity": "readable"
  }
}
```

### 4.4 判定标准
- **坏点数量**：≤3个
- **颜色准确度**：色差 ΔE < 5
- **亮度范围**：10-400 nits
- **响应时间**：显示切换 < 100ms

### 4.5 检测方法
- **图像捕获**：Android设备摄像头拍照
- **像素分析**：OpenCV图像处理算法
- **颜色测量**：色彩空间转换和色差计算

## 5. 测试执行流程

### 5.1 测试前准备
1. 检查测试环境（光线、噪声、网络）
2. 验证设备连接状态
3. 初始化测试参数
4. 准备测试数据和文件

### 5.2 测试执行步骤
1. 发送测试指令到机器狗
2. 监控测试执行状态
3. 收集测试数据和结果
4. 进行自动判定或人工确认
5. 记录测试结果和异常

### 5.3 测试后处理
1. 生成测试报告
2. 保存测试数据
3. 清理测试环境
4. 上传结果到服务器

## 6. 质量控制

### 6.1 测试环境标准化
- 光照条件：500-1000 lux
- 噪声水平：< 40dB
- 温度范围：20-25°C
- 湿度范围：40-60%

### 6.2 测试数据可追溯性
- 设备序列号记录
- 测试时间戳
- 测试人员信息
- 环境参数记录

### 6.3 异常处理机制
- 测试超时处理
- 设备无响应处理
- 网络中断处理
- 数据异常处理

## 7. 麦克风测试模块

### 7.1 测试目标
验证机器狗音频录制系统的完整功能，包括录音质量、噪声抑制、灵敏度和频率响应。

### 7.2 硬件要求
- 麦克风类型：MEMS/驻极体
- 频率响应：100Hz - 8kHz
- 信噪比：≥40dB
- 灵敏度：-38dBV/Pa
- 录音延迟：≤50ms

### 7.3 测试用例设计

#### 7.3.1 录音质量测试
```json
{
  "testCase": "recording_quality",
  "description": "测试录音质量和信噪比",
  "commands": [
    {"action": "start_record", "duration": 5000, "sampleRate": 44100, "format": "wav"},
    {"action": "play_and_record", "playTone": 1000, "volume": 70, "duration": 3000},
    {"action": "noise_test", "recordDuration": 5000, "backgroundNoise": "silent"}
  ],
  "expectedResults": {
    "snr": "≥40dB",
    "thd": "≤3%"
  }
}
```

#### 7.3.2 灵敏度测试
```json
{
  "testCase": "sensitivity_test",
  "description": "测试麦克风灵敏度",
  "commands": [
    {"action": "sensitivity_test", "testTones": [500, 1000, 2000, 4000], "volumes": [40, 60, 80]},
    {"action": "distance_test", "distances": [0.5, 1.0, 2.0], "tone": 1000, "volume": 70}
  ],
  "expectedResults": {
    "sensitivity": "-38dBV/Pa ±3dB",
    "distanceResponse": "predictable"
  }
}
```

### 7.4 判定标准
- **信噪比**：≥40dB
- **频率响应**：100Hz-8kHz
- **录音延迟**：≤50ms
- **总谐波失真**：≤3%

## 8. WiFi测试模块

### 8.1 测试目标
验证机器狗WiFi通信功能，包括连接稳定性、传输速率、信号质量和多频段支持。

### 8.2 硬件要求
- WiFi标准：802.11 b/g/n/ac
- 频段支持：2.4GHz + 5GHz
- 传输速率：2.4G ≥10Mbps, 5G ≥50Mbps
- 信号强度：≥-70dBm
- 连接成功率：≥95%

### 8.3 测试用例设计

#### 8.3.1 连接稳定性测试
```json
{
  "testCase": "connection_stability",
  "description": "测试WiFi连接稳定性",
  "commands": [
    {"action": "scan_networks", "timeout": 10000},
    {"action": "connect_test", "ssid": "TEST_AP_2G", "password": "test123", "attempts": 10},
    {"action": "connect_test", "ssid": "TEST_AP_5G", "password": "test123", "attempts": 10},
    {"action": "reconnect_test", "cycles": 5, "interval": 30000}
  ],
  "expectedResults": {
    "connectionSuccessRate": "≥95%",
    "reconnectTime": "≤10s"
  }
}
```

#### 8.3.2 传输性能测试
```json
{
  "testCase": "transmission_performance",
  "description": "测试WiFi传输性能",
  "commands": [
    {"action": "speed_test", "direction": "download", "duration": 30000, "frequency": "2.4G"},
    {"action": "speed_test", "direction": "upload", "duration": 30000, "frequency": "2.4G"},
    {"action": "speed_test", "direction": "download", "duration": 30000, "frequency": "5G"},
    {"action": "speed_test", "direction": "upload", "duration": 30000, "frequency": "5G"}
  ],
  "expectedResults": {
    "2G_download": "≥10Mbps",
    "2G_upload": "≥5Mbps",
    "5G_download": "≥50Mbps",
    "5G_upload": "≥25Mbps"
  }
}
```

### 8.4 判定标准
- **连接成功率**：≥95%
- **2.4G速率**：下载≥10Mbps，上传≥5Mbps
- **5G速率**：下载≥50Mbps，上传≥25Mbps
- **信号强度**：≥-70dBm

## 9. 蓝牙测试模块

### 9.1 测试目标
验证机器狗蓝牙通信功能，包括设备发现、连接稳定性、数据传输和兼容性。

### 9.2 硬件要求
- 蓝牙版本：≥4.0 (支持BLE)
- 传输速率：≥1Mbps
- 有效距离：≥10米
- 连接延迟：≤3秒
- 连接成功率：≥98%

### 9.3 测试用例设计

#### 9.3.1 设备发现和连接测试
```json
{
  "testCase": "discovery_connection",
  "description": "测试蓝牙设备发现和连接",
  "commands": [
    {"action": "enable_discoverable", "timeout": 30000},
    {"action": "scan_devices", "duration": 15000},
    {"action": "connection_test", "targetDevice": "test_phone", "attempts": 10},
    {"action": "pairing_test", "securityLevel": "high"}
  ],
  "expectedResults": {
    "discoveryTime": "≤10s",
    "connectionSuccessRate": "≥98%",
    "pairingTime": "≤5s"
  }
}
```

#### 9.3.2 数据传输测试
```json
{
  "testCase": "data_transmission",
  "description": "测试蓝牙数据传输性能",
  "commands": [
    {"action": "throughput_test", "dataSize": 1024, "direction": "both", "duration": 30000},
    {"action": "latency_test", "packetSize": 64, "packets": 100},
    {"action": "reliability_test", "dataSize": 10240, "cycles": 10}
  ],
  "expectedResults": {
    "throughput": "≥1Mbps",
    "latency": "≤100ms",
    "packetLoss": "≤1%"
  }
}
```

### 9.4 判定标准
- **连接成功率**：≥98%
- **传输速率**：≥1Mbps
- **有效距离**：≥10米
- **连接延迟**：≤3秒

## 10. 5G模块测试

### 10.1 测试目标
验证机器狗5G网络通信功能，包括网络注册、信号质量、数据传输和网络切换。

### 10.2 硬件要求
- 5G标准：NSA/SA
- 频段支持：Sub-6GHz
- 下载速率：≥100Mbps
- 上传速率：≥50Mbps
- 注册成功率：≥90%

### 10.3 测试用例设计

#### 10.3.1 网络注册测试
```json
{
  "testCase": "network_registration",
  "description": "测试5G网络注册功能",
  "commands": [
    {"action": "network_scan", "timeout": 30000},
    {"action": "register_test", "operator": "auto", "attempts": 5},
    {"action": "signal_quality", "duration": 60000, "interval": 5000}
  ],
  "expectedResults": {
    "registrationSuccessRate": "≥90%",
    "registrationTime": "≤60s",
    "rsrp": "≥-100dBm"
  }
}
```

### 10.4 判定标准
- **注册成功率**：≥90%
- **下载速率**：≥100Mbps
- **上传速率**：≥50Mbps
- **RSRP**：≥-100dBm

这个规范为后续的代码实现提供了详细的技术指导和验收标准。
